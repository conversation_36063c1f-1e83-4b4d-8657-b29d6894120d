package com.aliyun.sample;

import com.aliyun.alidns20150109.models.UpdateDomainRecordRequest;
import com.aliyun.alidns20150109.models.UpdateDomainRecordResponse;
import com.aliyun.sample.config.AliyunConfig;
import com.aliyun.teaopenapi.models.Config;
import java.util.logging.Logger;
import java.util.logging.Level;

/**
 * DNS记录服务类
 * 用于管理阿里云DNS域名记录的更新操作
 */
public class DnsRecordService {

    private static final Logger LOGGER = Logger.getLogger(DnsRecordService.class.getName());

    private final String accessKeyId;
    private final String accessKeySecret;
    private final String regionId;
    private volatile com.aliyun.alidns20150109.Client client;
    private final AliyunConfig config;

    /**
     * 构造函数
     * @param accessKeyId 阿里云AccessKey ID
     * @param accessKeySecret 阿里云AccessKey Secret
     * @param regionId 区域ID
     */
    public DnsRecordService(String accessKeyId, String accessKeySecret, String regionId) {
        this.accessKeyId = accessKeyId;
        this.accessKeySecret = accessKeySecret;
        this.regionId = regionId;
        this.config = AliyunConfig.getInstance();
    }

    /**
     * 默认构造函数，使用配置文件中的配置
     */
    public DnsRecordService() {
        this.config = AliyunConfig.getInstance();
        this.accessKeyId = config.getAccessKeyId();
        this.accessKeySecret = config.getAccessKeySecret();
        this.regionId = config.getRegionId();
    }

    /**
     * 初始化DNS客户端
     * @return DNS客户端实例
     * @throws Exception 初始化异常
     */
    public com.aliyun.alidns20150109.Client initializeClient() throws Exception {
        if (client == null) {
            Config config = new Config();
            config.accessKeyId = this.accessKeyId;
            config.accessKeySecret = this.accessKeySecret;
            config.regionId = this.regionId;
            client = new com.aliyun.alidns20150109.Client(config);
        }
        return client;
    }

    /**
     * 更新域名记录
     * @param recordId 记录ID
     * @param rr 主机记录
     * @param recordType 记录类型 (A, CNAME, MX等)
     * @param value 记录值
     * @return 更新响应
     * @throws Exception 更新异常
     */
    public UpdateDomainRecordResponse updateDomainRecord(String recordId, String rr, String recordType, String value) throws Exception {
        com.aliyun.alidns20150109.Client dnsClient = initializeClient();

        UpdateDomainRecordRequest request = new UpdateDomainRecordRequest();
        request.recordId = recordId;
        request.RR = rr;
        request.type = recordType;
        request.value = value;

        UpdateDomainRecordResponse response = dnsClient.updateDomainRecord(request);
        System.out.println("DNS记录更新成功: " + rr + " -> " + value);

        return response;
    }

    /**
     * 更新A记录
     * @param recordId 记录ID
     * @param rr 主机记录
     * @param ipAddress IP地址
     * @return 更新响应
     * @throws Exception 更新异常
     */
    public UpdateDomainRecordResponse updateARecord(String recordId, String rr, String ipAddress) throws Exception {
        return updateDomainRecord(recordId, rr, config.getDnsRecordType(), ipAddress);
    }

    /**
     * 使用配置文件进行DNS记录更新的便捷方法
     * @return 更新响应
     * @throws Exception 更新异常
     */
    public UpdateDomainRecordResponse updateDnsRecordFromConfig() throws Exception {
        return updateDomainRecord(
            config.getDnsRecordId(),
            config.getDnsRecordRr(),
            config.getDnsRecordType(),
            config.getDnsTargetIp()
        );
    }

    /**
     * 示例用法的主方法
     */
    public static void main(String[] args) throws Exception {
        // 使用配置文件创建服务实例
        DnsRecordService service = new DnsRecordService();

        // 使用配置文件中的参数更新DNS记录
        service.updateDnsRecordFromConfig();
    }
}
