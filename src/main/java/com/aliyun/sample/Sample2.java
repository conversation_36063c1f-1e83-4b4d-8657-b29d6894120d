package com.aliyun.sample;

import com.aliyun.alidns20150109.models.UpdateDomainRecordRequest;
import com.aliyun.alidns20150109.models.UpdateDomainRecordResponse;
import com.aliyun.teaopenapi.models.Config;

/**
 * DNS记录服务类
 * 用于管理阿里云DNS域名记录的更新操作
 */
public class DnsRecordService {

    private final String accessKeyId;
    private final String accessKeySecret;
    private final String regionId;
    private com.aliyun.alidns20150109.Client client;

    // 默认配置
    public static final String DEFAULT_TARGET_IP = "**************";
    public static final String DEFAULT_RECORD_TYPE = "A";

    /**
     * 构造函数
     * @param accessKeyId 阿里云AccessKey ID
     * @param accessKeySecret 阿里云AccessKey Secret
     * @param regionId 区域ID
     */
    public DnsRecordService(String accessKeyId, String accessKeySecret, String regionId) {
        this.accessKeyId = accessKeyId;
        this.accessKeySecret = accessKeySecret;
        this.regionId = regionId;
    }

    /**
     * 初始化DNS客户端
     * @return DNS客户端实例
     * @throws Exception 初始化异常
     */
    public com.aliyun.alidns20150109.Client initializeClient() throws Exception {
        if (client == null) {
            Config config = new Config();
            config.accessKeyId = this.accessKeyId;
            config.accessKeySecret = this.accessKeySecret;
            config.regionId = this.regionId;
            client = new com.aliyun.alidns20150109.Client(config);
        }
        return client;
    }

    /**
     * 更新域名记录
     * @param recordId 记录ID
     * @param rr 主机记录
     * @param recordType 记录类型 (A, CNAME, MX等)
     * @param value 记录值
     * @return 更新响应
     * @throws Exception 更新异常
     */
    public UpdateDomainRecordResponse updateDomainRecord(String recordId, String rr, String recordType, String value) throws Exception {
        com.aliyun.alidns20150109.Client dnsClient = initializeClient();

        UpdateDomainRecordRequest request = new UpdateDomainRecordRequest();
        request.recordId = recordId;
        request.RR = rr;
        request.type = recordType;
        request.value = value;

        UpdateDomainRecordResponse response = dnsClient.updateDomainRecord(request);
        System.out.println("DNS记录更新成功: " + rr + " -> " + value);

        return response;
    }

    /**
     * 更新A记录
     * @param recordId 记录ID
     * @param rr 主机记录
     * @param ipAddress IP地址
     * @return 更新响应
     * @throws Exception 更新异常
     */
    public UpdateDomainRecordResponse updateARecord(String recordId, String rr, String ipAddress) throws Exception {
        return updateDomainRecord(recordId, rr, DEFAULT_RECORD_TYPE, ipAddress);
    }

    /**
     * 示例用法的主方法
     */
    public static void main(String[] args) throws Exception {
        // 创建服务实例
        DnsRecordService service = new DnsRecordService(
            "LTAI5tEwdj5DuPYzLn5yvQoT",
            "******************************",
            "cn-hangzhou"
        );

        // 更新DNS记录
        service.updateARecord(
            "885487535054297088",
            "slb-inner",
            DEFAULT_TARGET_IP
        );
    }
}
