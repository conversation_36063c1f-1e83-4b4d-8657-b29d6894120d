package com.aliyun.sample;

import com.aliyun.sample.config.AliyunConfig;
import com.aliyun.teaopenapi.models.Config;
import com.aliyun.vpc20160428.models.*;
import java.util.logging.Logger;
import java.util.logging.Level;

/**
 * VPC转发表服务类
 * 用于管理VPC转发表条目，包括查询和修改转发条目的内部IP
 */
public class VpcForwardTableService {

    private static final Logger LOGGER = Logger.getLogger(VpcForwardTableService.class.getName());
    private static final int DEFAULT_PAGE_SIZE = 50;
    private static final int DEFAULT_PAGE_NUMBER = 1;

    private final String accessKeyId;
    private final String accessKeySecret;
    private final String regionId;
    private volatile com.aliyun.vpc20160428.Client client;
    private final AliyunConfig config;

    /**
     * 构造函数
     * @param accessKeyId 阿里云AccessKey ID
     * @param accessKeySecret 阿里云AccessKey Secret
     * @param regionId 区域ID
     * @throws IllegalArgumentException 参数为空时抛出
     */
    public VpcForwardTableService(String accessKeyId, String accessKeySecret, String regionId) {
        validateNotBlank(accessKeyId, "AccessKey ID不能为空");
        validateNotBlank(accessKeySecret, "AccessKey Secret不能为空");
        validateNotBlank(regionId, "区域ID不能为空");

        this.accessKeyId = accessKeyId;
        this.accessKeySecret = accessKeySecret;
        this.regionId = regionId;
        this.config = AliyunConfig.getInstance();

        LOGGER.info("初始化VPC转发表服务，区域: " + regionId);
    }

    /**
     * 默认构造函数，使用配置文件中的配置
     */
    public VpcForwardTableService() {
        this.config = AliyunConfig.getInstance();
        this.accessKeyId = config.getAccessKeyId();
        this.accessKeySecret = config.getAccessKeySecret();
        this.regionId = config.getRegionId();

        LOGGER.info("使用配置文件初始化VPC转发表服务，区域: " + regionId);
    }

    /**
     * 初始化VPC客户端（线程安全）
     * @return VPC客户端实例
     * @throws Exception 初始化异常
     */
    public com.aliyun.vpc20160428.Client initializeClient() throws Exception {
        if (client == null) {
            synchronized (this) {
                if (client == null) {
                    try {
                        Config config = new Config();
                        config.accessKeyId = this.accessKeyId;
                        config.accessKeySecret = this.accessKeySecret;
                        config.regionId = this.regionId;
                        client = new com.aliyun.vpc20160428.Client(config);
                        LOGGER.info("VPC客户端初始化成功");
                    } catch (Exception e) {
                        LOGGER.log(Level.SEVERE, "VPC客户端初始化失败", e);
                        throw new Exception("VPC客户端初始化失败: " + e.getMessage(), e);
                    }
                }
            }
        }
        return client;
    }

    /**
     * 查询转发表条目
     * @param forwardTableId 转发表ID
     * @param internalIp 内部IP
     * @return 转发表条目响应
     * @throws Exception 查询异常
     */
    public DescribeForwardTableEntriesResponse describeForwardTableEntries(String forwardTableId, String internalIp) throws Exception {
        validateNotBlank(forwardTableId, "转发表ID不能为空");
        validateNotBlank(internalIp, "内部IP不能为空");

        com.aliyun.vpc20160428.Client vpcClient = initializeClient();

        DescribeForwardTableEntriesRequest request = new DescribeForwardTableEntriesRequest();
        request.regionId = this.regionId;
        request.forwardTableId = forwardTableId;
        request.internalIp = internalIp;
        request.pageSize = DEFAULT_PAGE_SIZE;
        request.pageNumber = DEFAULT_PAGE_NUMBER;

        LOGGER.info("查询转发表条目，表ID: " + forwardTableId + ", 内部IP: " + internalIp);
        return vpcClient.describeForwardTableEntries(request);
    }

    /**
     * 修改转发条目的内部IP
     * @param forwardTableId 转发表ID
     * @param originalInternalIp 原始内部IP
     * @param targetInternalIp 目标内部IP
     * @throws Exception 修改异常
     */
    public void modifyForwardEntryInternalIp(String forwardTableId, String originalInternalIp, String targetInternalIp) throws Exception {
        validateNotBlank(targetInternalIp, "目标内部IP不能为空");

        DescribeForwardTableEntriesResponse response = describeForwardTableEntries(forwardTableId, originalInternalIp);

        if (response.body.forwardTableEntries == null || response.body.forwardTableEntries.forwardTableEntry == null) {
            LOGGER.warning("未找到匹配的转发表条目");
            return;
        }

        int successCount = 0;
        int failCount = 0;

        for (DescribeForwardTableEntriesResponseBodyForwardTableEntriesForwardTableEntry item : response.body.forwardTableEntries.forwardTableEntry) {
            LOGGER.info("处理转发条目ID: " + item.forwardEntryId);

            try {
                modifyForwardEntry(item.forwardEntryId, item.forwardTableId, targetInternalIp);
                LOGGER.info("修改成功: " + item.internalIp + " -> " + targetInternalIp);
                successCount++;
            } catch (Exception e) {
                LOGGER.log(Level.WARNING, "修改转发条目失败: " + e.getMessage(), e);
                failCount++;
            }
        }

        LOGGER.info("转发条目修改完成，成功: " + successCount + ", 失败: " + failCount);
    }

    /**
     * 修改单个转发条目
     * @param forwardEntryId 转发条目ID
     * @param forwardTableId 转发表ID
     * @param newInternalIp 新的内部IP
     * @throws Exception 修改异常
     */
    private void modifyForwardEntry(String forwardEntryId, String forwardTableId, String newInternalIp) throws Exception {
        com.aliyun.vpc20160428.Client vpcClient = initializeClient();

        ModifyForwardEntryRequest request = new ModifyForwardEntryRequest();
        request.regionId = this.regionId;
        request.forwardEntryId = forwardEntryId;
        request.forwardTableId = forwardTableId;
        request.internalIp = newInternalIp;

        vpcClient.modifyForwardEntry(request);
    }

    /**
     * 参数验证工具方法
     * @param value 待验证的值
     * @param message 错误消息
     * @throws IllegalArgumentException 验证失败时抛出
     */
    private void validateNotBlank(String value, String message) {
        if (value == null || value.trim().isEmpty()) {
            throw new IllegalArgumentException(message);
        }
    }

    /**
     * 关闭资源
     */
    public void close() {
        if (client != null) {
            LOGGER.info("关闭VPC客户端资源");
            // 阿里云SDK客户端通常不需要显式关闭，但可以在这里添加清理逻辑
            client = null;
        }
    }

    /**
     * 使用配置文件进行转发条目IP修改的便捷方法
     * @throws Exception 修改异常
     */
    public void modifyForwardEntryInternalIpFromConfig() throws Exception {
        modifyForwardEntryInternalIp(
            config.getVpcForwardTableId(),
            config.getVpcOriginalInternalIp(),
            config.getVpcTargetInternalIp()
        );
    }

    /**
     * 示例用法的主方法
     */
    public static void main(String[] args) throws Exception {
        // 使用配置文件创建服务实例
        VpcForwardTableService service = new VpcForwardTableService();

        try {
            // 使用配置文件中的参数修改转发条目的内部IP
            service.modifyForwardEntryInternalIpFromConfig();
        } finally {
            // 清理资源
            service.close();
        }
    }
}
