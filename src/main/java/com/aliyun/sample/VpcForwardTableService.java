package com.aliyun.sample;

import com.aliyun.teaopenapi.models.Config;
import com.aliyun.vpc20160428.models.*;

/**
 * VPC转发表服务类
 * 用于管理VPC转发表条目，包括查询和修改转发条目的内部IP
 */
public class VpcForwardTableService {

    private final String accessKeyId;
    private final String accessKeySecret;
    private final String regionId;
    private com.aliyun.vpc20160428.Client client;

    // 默认IP配置
    public static final String DEFAULT_ORIGINAL_INTERNAL_IP = "*************";
    public static final String DEFAULT_TARGET_INTERNAL_IP = "**************";

    /**
     * 构造函数
     * @param accessKeyId 阿里云AccessKey ID
     * @param accessKeySecret 阿里云AccessKey Secret
     * @param regionId 区域ID
     */
    public VpcForwardTableService(String accessKeyId, String accessKeySecret, String regionId) {
        this.accessKeyId = accessKeyId;
        this.accessKeySecret = accessKeySecret;
        this.regionId = regionId;
    }

    /**
     * 初始化VPC客户端
     * @return VPC客户端实例
     * @throws Exception 初始化异常
     */
    public com.aliyun.vpc20160428.Client initializeClient() throws Exception {
        if (client == null) {
            Config config = new Config();
            config.accessKeyId = this.accessKeyId;
            config.accessKeySecret = this.accessKeySecret;
            config.regionId = this.regionId;
            client = new com.aliyun.vpc20160428.Client(config);
        }
        return client;
    }

    /**
     * 查询转发表条目
     * @param forwardTableId 转发表ID
     * @param internalIp 内部IP
     * @return 转发表条目响应
     * @throws Exception 查询异常
     */
    public DescribeForwardTableEntriesResponse describeForwardTableEntries(String forwardTableId, String internalIp) throws Exception {
        com.aliyun.vpc20160428.Client vpcClient = initializeClient();

        DescribeForwardTableEntriesRequest request = new DescribeForwardTableEntriesRequest();
        request.regionId = this.regionId;
        request.forwardTableId = forwardTableId;
        request.internalIp = internalIp;
        request.pageSize = 50;
        request.pageNumber = 1;

        return vpcClient.describeForwardTableEntries(request);
    }

    /**
     * 修改转发条目的内部IP
     * @param forwardTableId 转发表ID
     * @param originalInternalIp 原始内部IP
     * @param targetInternalIp 目标内部IP
     * @throws Exception 修改异常
     */
    public void modifyForwardEntryInternalIp(String forwardTableId, String originalInternalIp, String targetInternalIp) throws Exception {
        DescribeForwardTableEntriesResponse response = describeForwardTableEntries(forwardTableId, originalInternalIp);

        response.body.forwardTableEntries.forwardTableEntry.forEach(item -> {
            System.out.println("处理转发条目ID: " + item.forwardEntryId);

            try {
                modifyForwardEntry(item.forwardEntryId, item.forwardTableId, targetInternalIp);
                System.out.println("修改成功: " + item.internalIp + " -> " + targetInternalIp);
            } catch (Exception e) {
                System.err.println("修改转发条目失败: " + e.getMessage());
                e.printStackTrace();
            }
        });
    }

    /**
     * 修改单个转发条目
     * @param forwardEntryId 转发条目ID
     * @param forwardTableId 转发表ID
     * @param newInternalIp 新的内部IP
     * @throws Exception 修改异常
     */
    private void modifyForwardEntry(String forwardEntryId, String forwardTableId, String newInternalIp) throws Exception {
        com.aliyun.vpc20160428.Client vpcClient = initializeClient();

        ModifyForwardEntryRequest request = new ModifyForwardEntryRequest();
        request.regionId = this.regionId;
        request.forwardEntryId = forwardEntryId;
        request.forwardTableId = forwardTableId;
        request.internalIp = newInternalIp;

        vpcClient.modifyForwardEntry(request);
    }

    /**
     * 示例用法的主方法
     */
    public static void main(String[] args) throws Exception {
        // 创建服务实例
        VpcForwardTableService service = new VpcForwardTableService(
            "LTAI5tEwdj5DuPYzLn5yvQoT",
            "******************************",
            "cn-hangzhou"
        );

        // 修改转发条目的内部IP
        service.modifyForwardEntryInternalIp(
            "ftb-bp1lkdf91b9d17eoj1tz7",
            DEFAULT_ORIGINAL_INTERNAL_IP,
            DEFAULT_TARGET_INTERNAL_IP
        );
    }
}
