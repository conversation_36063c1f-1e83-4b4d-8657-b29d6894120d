package com.aliyun.sample.config;

import java.io.IOException;
import java.io.InputStream;
import java.util.Properties;

/**
 * 阿里云配置管理类
 * 用于加载和管理配置文件中的配置项
 */
public class AliyunConfig {
    
    private static final String CONFIG_FILE = "application.properties";
    private static AliyunConfig instance;
    private Properties properties;
    
    private AliyunConfig() {
        loadProperties();
    }
    
    /**
     * 获取配置实例（单例模式）
     * @return 配置实例
     */
    public static synchronized AliyunConfig getInstance() {
        if (instance == null) {
            instance = new AliyunConfig();
        }
        return instance;
    }
    
    /**
     * 加载配置文件
     */
    private void loadProperties() {
        properties = new Properties();
        try (InputStream inputStream = getClass().getClassLoader().getResourceAsStream(CONFIG_FILE)) {
            if (inputStream == null) {
                throw new RuntimeException("配置文件 " + CONFIG_FILE + " 未找到");
            }
            properties.load(inputStream);
            validateRequiredProperties();
        } catch (IOException e) {
            throw new RuntimeException("加载配置文件失败: " + e.getMessage(), e);
        }
    }

    /**
     * 验证必需的配置项
     */
    private void validateRequiredProperties() {
        String[] requiredKeys = {
            "aliyun.access-key-id", "aliyun.access-key-secret", "aliyun.region-id"
        };

        for (String key : requiredKeys) {
            String value = properties.getProperty(key);
            if (value == null || value.trim().isEmpty()) {
                throw new RuntimeException("必需的配置项 " + key + " 未设置或为空");
            }
        }
    }
    
    /**
     * 获取配置值
     * @param key 配置键
     * @return 配置值
     */
    public String getProperty(String key) {
        return properties.getProperty(key);
    }
    
    /**
     * 获取配置值，如果不存在则返回默认值
     * @param key 配置键
     * @param defaultValue 默认值
     * @return 配置值或默认值
     */
    public String getProperty(String key, String defaultValue) {
        return properties.getProperty(key, defaultValue);
    }
    
    // 阿里云基础配置
    public String getAccessKeyId() {
        return getProperty("aliyun.access-key-id");
    }
    
    public String getAccessKeySecret() {
        return getProperty("aliyun.access-key-secret");
    }
    
    public String getRegionId() {
        return getProperty("aliyun.region-id");
    }
    
    // VPC配置
    public String getVpcForwardTableId() {
        return getProperty("vpc.forward-table-id");
    }
    
    public String getVpcOriginalInternalIp() {
        return getProperty("vpc.original-internal-ip");
    }
    
    public String getVpcTargetInternalIp() {
        return getProperty("vpc.target-internal-ip");
    }
    
    // DNS配置
    public String getDnsRecordId() {
        return getProperty("dns.record-id");
    }
    
    public String getDnsRecordRr() {
        return getProperty("dns.record-rr");
    }
    
    public String getDnsRecordType() {
        return getProperty("dns.record-type", "A");
    }
    
    public String getDnsTargetIp() {
        return getProperty("dns.target-ip");
    }
}
